# HustleBot Project Rules & Standards

## Project Overview
**Name**: <PERSON><PERSON>leBot  
**Type**: Telegram <PERSON> with AI Integration  
**Tech Stack**: Node.js, Telegram Bot API, Agent Hustle AI  
**Architecture**: Event-driven bot with scheduled task automation  

## Core Principles

### 1. Security First
- **Environment Variables**: All sensitive data (API keys, tokens) must be stored in `.env` files
- **Input Validation**: All user inputs must be sanitized and validated
- **Admin Authorization**: Admin commands require explicit user ID verification
- **Secure Logging**: Never log sensitive information (API keys, user data)

### 2. Code Structure & Organization
- **Single Responsibility**: Each function should have one clear purpose
- **Modular Design**: Keep related functionality grouped together
- **Error Handling**: Comprehensive try-catch blocks for all async operations
- **Configuration**: Use centralized CONFIG object for constants

### 3. Telegram Bot Standards
- **Message Limits**: Respect Telegram's 4096 character message limit
- **Rate Limiting**: Implement proper delays and throttling
- **Command Structure**: All commands must start with `/` and be lowercase
- **Response Formatting**: Use appropriate Markdown escaping for formatted messages

## Development Guidelines

### File Structure Rules
```
project-root/
├── app.js                 # Main application entry point
├── .env                   # Environment variables (never commit)
├── package.json           # Dependencies and scripts
├── scheduled_tasks.json   # Persistent task storage
├── agents-agument/        # Agent system documentation
└── docs/                  # Project documentation
```

### Code Standards

#### Function Naming
- Use descriptive, action-oriented names
- Prefix validation functions with `validate`
- Prefix utility functions with appropriate context
- Example: `sanitizeEnvVar()`, `validateUserInput()`, `secureLog()`

#### Error Handling Pattern
```javascript
try {
  // Main operation
  await someAsyncOperation();
  secureLog('success', 'Operation completed');
} catch (error) {
  secureLog('error', `Operation failed: ${error.message}`);
  // User-friendly error response
  await bot.sendMessage(chatId, 'Sorry, something went wrong.');
}
```

#### Logging Standards
- Use `secureLog()` function for all logging
- Include timestamps and log levels
- Log types: `command`, `error`, `info`, `success`
- Never log sensitive data

### Agent System Integration

#### Agent Documentation Structure
All agent files must follow this format:
```markdown
---
name: agent-name
description: Brief description of agent purpose
tools: list, of, required, tools
---

# Agent Name

## Mission
Clear statement of agent purpose

## Standard Workflow
1. Step-by-step process
2. Decision points
3. Handoff criteria

## Required Output Format
Standardized output structure
```

#### Agent Handoff Rules
- Use standardized trigger conditions
- Include complete context in handoffs
- Document all agent interactions
- Maintain audit trail

### Automated Task Management

#### Task Configuration
- **Naming**: Use descriptive, unique task names
- **Scheduling**: Validate cron expressions before creation
- **Prompts**: Store multiple prompts for variety
- **Persistence**: Save all tasks to `scheduled_tasks.json`

#### Task Security
- Admin-only task management
- Validate target group IDs
- Sanitize all prompt inputs
- Log all task operations

### API Integration Standards

#### Agent Hustle Integration
- Use vault IDs for conversation context
- Implement proper timeout handling
- Handle streaming responses appropriately
- Validate response formats

#### Error Recovery
- Implement retry logic with exponential backoff
- Graceful degradation for API failures
- User-friendly error messages
- Comprehensive error logging

## Testing Requirements

### Unit Testing
- Test all validation functions
- Mock external API calls
- Test error handling paths
- Validate input sanitization

### Integration Testing
- Test Telegram bot commands
- Verify Agent Hustle integration
- Test scheduled task execution
- Validate persistence mechanisms

### Security Testing
- Test admin authorization
- Validate input sanitization
- Test rate limiting
- Verify environment variable handling

## Deployment Standards

### Environment Setup
- Node.js v16 or higher
- All dependencies in package.json
- Environment variables documented
- Timezone configuration

### Production Considerations
- Enable production logging
- Configure proper error monitoring
- Set up health checks
- Implement graceful shutdown

## Maintenance Guidelines

### Code Reviews
- Security review for all changes
- Performance impact assessment
- Documentation updates
- Test coverage verification

### Updates & Dependencies
- Regular dependency updates
- Security vulnerability scanning
- Backward compatibility checks
- Migration planning

### Monitoring
- Bot uptime monitoring
- Error rate tracking
- Performance metrics
- User activity logging

## Compliance & Standards

### Data Privacy
- Minimal data collection
- Secure data storage
- User consent for data usage
- Data retention policies

### Code Quality
- ESLint configuration
- Consistent formatting
- Documentation requirements
- Version control standards

---

**Last Updated**: 2025-01-08  
**Version**: 1.0  
**Maintainer**: Project Team
